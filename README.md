# ASTChunk

This repository contains code for AST-based code chunking that preserves syntactic structure and semantic boundaries. ASTChunk intelligently divides source code into meaningful chunks while respecting the Abstract Syntax Tree (AST) structure, making it ideal for code analysis, documentation generation, and machine learning applications.

<!-- Add paper citation when available -->
<!--
This work is described in the following paper:  
>[Paper Title](paper_url)  
> Author Names
> Conference/Journal, Year

Bibtex for citations:
```bibtex
@inproceedings{<citation_key>,
    title = "<Paper Title>",
    author = "<Authors>",
    booktitle = "<Conference>",
    year = "<Year>",
    url = "<URL>",
    pages = "<Pages>",
}
```
-->

<!--
## Features

- **Structure-aware chunking**: Respects AST boundaries to avoid breaking syntactic constructs
- **Multi-language support**: Python, Java, C#, and TypeScript
- **Configurable chunk sizes**: Based on non-whitespace character count for consistent sizing
- **Metadata preservation**: Maintains file paths, line numbers, and AST context
- **Overlapping support**: Optional overlapping between chunks for better context
- **Efficient processing**: O(1) chunk size lookup with preprocessing
-->

## Installation

From PyPI:
```bash
pip install astchunk
```

From source:
```bash
<NAME_EMAIL>:yilinjz/astchunk.git
cd src/astchunk
pip install -e .
```

ASTChunk depends on [tree-sitter](https://tree-sitter.github.io/tree-sitter/) for parsing. The required language parsers are automatically installed:

```bash
# Core dependencies (automatically installed)
pip install numpy pyrsistent tree-sitter
pip install tree-sitter-python tree-sitter-java tree-sitter-c-sharp tree-sitter-typescript
```

## Quick Start

```python
from astchunk import ASTChunkBuilder

# Initialize the chunk builder
chunk_builder = ASTChunkBuilder(
    max_chunk_size=2000,        # Maximum non-whitespace characters per chunk
    language="python",          # Supported: python, java, csharp, typescript
    metadata_template="default" # Metadata format for output
)

# Your source code
code = """
def fibonacci(n):
    if n <= 1:
        return n
    return fibonacci(n-1) + fibonacci(n-2)

class Calculator:
    def add(self, a, b):
        return a + b
    
    def multiply(self, a, b):
        return a * b
"""

# Create chunks
chunks = chunk_builder.chunkify(code)

# Each chunk contains content and metadata
for i, chunk in enumerate(chunks):
    print(f"Chunk {i+1}:")
    print(f"Content: {chunk['content']}")
    print(f"Metadata: {chunk['metadata']}")
    print("-" * 50)
```

## Advanced Usage

### Customizing Chunk Parameters

```python
chunks = chunk_builder.chunkify(
    code,
    # Add file metadata
    filepath="src/calculator.py",
    
    # Enable overlapping between chunks
    overlapping_range=2,
    
    # Add chunk expansion (metadata headers)
    chunk_expansion=True
)
```

### Processing Multiple Languages

```python
# Python code
python_builder = ASTChunkBuilder(
    max_chunk_size=1500,
    language="python",
    metadata_template="default"
)

# Java code  
java_builder = ASTChunkBuilder(
    max_chunk_size=2000,
    language="java", 
    metadata_template="default"
)

# TypeScript code
ts_builder = ASTChunkBuilder(
    max_chunk_size=1800,
    language="typescript",
    metadata_template="default"
)
```

### Working with Files

```python
# Process a single file
with open("example.py", "r") as f:
    code = f.read()

chunks = chunk_builder.chunkify(
    code,
    filepath="example.py"
)

# Save chunks to separate files
for i, chunk in enumerate(chunks):
    with open(f"chunk_{i+1}.py", "w") as f:
        f.write(chunk['content'])
```

### Metadata Templates

Different metadata templates for various use cases:

```python
# For code documentation
doc_builder = ASTChunkBuilder(
    max_chunk_size=2000,
    language="python",
    metadata_template="documentation"
)

# For machine learning datasets
ml_builder = ASTChunkBuilder(
    max_chunk_size=1500,
    language="python", 
    metadata_template="ml_dataset"
)
```

## Core Functions

### Preprocessing Functions

```python
from astchunk.preprocessing import preprocess_nws_count, get_nws_count, ByteRange

# Preprocess code for efficient size calculation
code_bytes = code.encode('utf-8')
nws_cumsum = preprocess_nws_count(code_bytes)

# Get non-whitespace character count for any byte range
byte_range = ByteRange(0, 100)  # First 100 bytes
char_count = get_nws_count(nws_cumsum, byte_range)
```

### Direct AST Processing

```python
from astchunk.astnode import ASTNode
from astchunk.astchunk import ASTChunk

# Work directly with AST nodes and chunks for custom processing
# (See API documentation for detailed usage)
```

## Supported Languages

| Language   | File Extensions | Status |
|------------|----------------|---------|
| Python     | `.py`          | ✅ Full support |
| Java       | `.java`        | ✅ Full support |
| C#         | `.cs`          | ✅ Full support |
| TypeScript | `.ts`, `.tsx`  | ✅ Full support |

## Configuration Options

- **`max_chunk_size`**: Maximum non-whitespace characters per chunk
- **`language`**: Programming language for parsing
- **`metadata_template`**: Format for chunk metadata
- **`overlapping_range`**: Number of AST nodes to overlap between chunks
- **`chunk_expansion`**: Whether to add metadata headers to chunks

## Contributing

We welcome contributions! Please see our [contributing guidelines](<CONTRIBUTING_URL>) for details.

## License

This project is licensed under the <LICENSE_TYPE> - see the [LICENSE](LICENSE) file for details.

## Version

Current version: 0.1.0
