[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "astchunk"
version = "0.1.0"
description = "AST-based code chunking library for improved code analysis and processing"
readme = "README.md"
license = {file = "LICENSE"}
authors = [
    {name = "<PERSON><PERSON> (<PERSON>) <PERSON>", email = "<EMAIL>"},
    {name = "<PERSON><PERSON><PERSON>", email = "<EMAIL>"},
    {name = "<PERSON><PERSON>", email = "<EMAIL>"},
    {name = "<PERSON><PERSON>", email = "<EMAIL>"},
    {name = "<PERSON><PERSON><PERSON>", email = "<EMAIL>"},
    {name = "<PERSON><PERSON>", email = "<EMAIL>"},
]
maintainers = [
    {name = "<PERSON><PERSON> (<PERSON>", email = "<EMAIL>"}
]
keywords = ["ast", "chunking", "code analysis", "code indexing", "code retrieval", "code generation", "tree-sitter", "parsing"]
classifiers = [
    "Development Status :: 3 - Alpha",
    "Intended Audience :: Science/Research",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.8",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Topic :: Software Development :: Libraries :: Python Modules",
    "Topic :: Software Development :: Code Generators",
    "Topic :: Text Processing :: Linguistic"
]
requires-python = ">=3.8"
dependencies = [
    "numpy>=1.20.0",
    "pyrsistent>=0.18.0",
    "tree-sitter>=0.20.0",
    "tree-sitter-python>=0.20.0",
    "tree-sitter-java>=0.20.0",
    "tree-sitter-c-sharp>=0.20.0",
    "tree-sitter-typescript>=0.20.0"
]

[project.optional-dependencies]
dev = [
    "pytest>=7.0.0",
    "pytest-cov>=4.0.0",
    "black>=22.0.0",
    "isort>=5.10.0",
    "flake8>=5.0.0",
    "mypy>=1.0.0",
    "pre-commit>=2.20.0"
]
docs = [
    "sphinx>=5.0.0",
    "sphinx-rtd-theme>=1.0.0",
    "myst-parser>=0.18.0"
]
test = [
    "pytest>=7.0.0",
    "pytest-cov>=4.0.0",
    "pytest-xdist>=2.5.0"
]

[project.urls]
Homepage = "https://github.com/yilinjz/astchunk"

[project.scripts]
astchunk = "astchunk.cli:main"

[tool.setuptools]
package-dir = {"" = "src"}

[tool.setuptools.packages.find]
where = ["src"]

[tool.setuptools.package-data]
astchunk = ["py.typed"]

# Black configuration
[tool.black]
line-length = 88
target-version = ['py38', 'py39', 'py310', 'py311']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

# isort configuration
[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["astchunk"]

# pytest configuration
[tool.pytest.ini_options]
minversion = "7.0"
addopts = [
    "--strict-markers",
    "--strict-config",
    "--cov=astchunk",
    "--cov-report=term-missing",
    "--cov-report=html",
    "--cov-report=xml"
]
testpaths = ["test"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]

# mypy configuration
[tool.mypy]
python_version = "3.8"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[[tool.mypy.overrides]]
module = [
    "tree_sitter.*",
    "pyrsistent.*"
]
ignore_missing_imports = true

# Coverage configuration
[tool.coverage.run]
source = ["src/astchunk"]
omit = [
    "*/tests/*",
    "*/test_*",
    "*/__pycache__/*"
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:"
]

# bumpver configuration
[tool.bumpver]
current_version = "0.1.0"
version_pattern = "MAJOR.MINOR.PATCH"
commit_message = "Bump version {old_version} -> {new_version}"
commit = true
tag = true
push = false

[tool.bumpver.file_patterns]
"pyproject.toml" = [
    'current_version = "{version}"', 'version = "{version}"'
]
"README.md" = [
    "{version}",
]
