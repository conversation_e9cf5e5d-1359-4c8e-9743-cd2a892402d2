from astchunk.astchunk_builder import ASTChunkBuilder

# Initialize the chunk builder
chunk_builder = ASTChunkBuilder(
    max_chunk_size=2000,        # Maximum non-whitespace characters per chunk
    language="python",          # Supported: python, java, csharp, typescript
    metadata_template="default" # Metadata format for output
)

# Your source code
code = """
def fibonacci(n):
    if n <= 1:
        return n
    return fibonacci(n-1) + fibonacci(n-2)

class Calculator:
    def add(self, a, b):
        return a + b
    
    def multiply(self, a, b):
        return a * b
"""

# Create chunks
chunks = chunk_builder.chunkify(code)

# Each chunk contains content and metadata
for i, chunk in enumerate(chunks):
    print(f"Chunk {i+1}:")
    print(f"Content: {chunk['content']}")
    print(f"Metadata: {chunk['metadata']}")
    print("-" * 50)